#include "Commands/UnrealMCPSplineCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "EditorAssetLibrary.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"

DEFINE_LOG_CATEGORY_STATIC(LogUnrealMCPSpline, Log, All);

FUnrealMCPSplineCommands::FUnrealMCPSplineCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleCommand(const FString &CommandType, const TSharedPtr<FJsonObject> &Params)
{
    if (CommandType == TEXT("set_spline_mesh_properties"))
    {
        return HandleSetSplineMeshProperties(Params);
    }
    else if (CommandType == TEXT("set_spline_mesh_start_and_end"))
    {
        return HandleSetSplineMeshStartAndEnd(Params);
    }
    else if (CommandType == TEXT("set_spline_mesh_start_scale"))
    {
        return HandleSetSplineMeshStartScale(Params);
    }
    else if (CommandType == TEXT("set_spline_mesh_end_scale"))
    {
        return HandleSetSplineMeshEndScale(Params);
    }
    else if (CommandType == TEXT("add_spline_point"))
    {
        return HandleAddSplinePoint(Params);
    }
    else if (CommandType == TEXT("set_spline_point_location"))
    {
        return HandleSetSplinePointLocation(Params);
    }
    else if (CommandType == TEXT("get_location_at_spline_point"))
    {
        return HandleGetLocationAtSplinePoint(Params);
    }
    else if (CommandType == TEXT("set_spline_tangent_at_point"))
    {
        return HandleSetSplineTangentAtPoint(Params);
    }
    else if (CommandType == TEXT("test_spline_params"))
    {
        return HandleTestSplineParams(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown spline command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleSetSplineMeshProperties(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    FString StaticMeshPath;
    if (!Params->TryGetStringField(TEXT("static_mesh"), StaticMeshPath))
    {
        StaticMeshPath = TEXT("/Engine/BasicShapes/Cube.Cube");
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineMeshComponent
    USplineMeshComponent *SplineMeshComponent = Cast<USplineMeshComponent>(ComponentNode->ComponentTemplate);
    if (!SplineMeshComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineMeshComponent"), *ComponentName));
    }

    // Load the static mesh
    UStaticMesh *StaticMesh = Cast<UStaticMesh>(UEditorAssetLibrary::LoadAsset(StaticMeshPath));
    if (!StaticMesh)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to load static mesh: %s"), *StaticMeshPath));
    }

    // Set the static mesh
    SplineMeshComponent->SetStaticMesh(StaticMesh);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    ResultObj->SetStringField(TEXT("static_mesh"), StaticMeshPath);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleSetSplineMeshStartAndEnd(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    // Get start position and tangent
    const TArray<TSharedPtr<FJsonValue>> *StartPosArray;
    if (!Params->TryGetArrayField(TEXT("start_position"), StartPosArray) || StartPosArray->Num() != 3)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid start_position parameter (expected [x, y, z])"));
    }

    const TArray<TSharedPtr<FJsonValue>> *StartTangentArray;
    if (!Params->TryGetArrayField(TEXT("start_tangent"), StartTangentArray) || StartTangentArray->Num() != 3)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid start_tangent parameter (expected [x, y, z])"));
    }

    const TArray<TSharedPtr<FJsonValue>> *EndPosArray;
    if (!Params->TryGetArrayField(TEXT("end_position"), EndPosArray) || EndPosArray->Num() != 3)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid end_position parameter (expected [x, y, z])"));
    }

    const TArray<TSharedPtr<FJsonValue>> *EndTangentArray;
    if (!Params->TryGetArrayField(TEXT("end_tangent"), EndTangentArray) || EndTangentArray->Num() != 3)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid end_tangent parameter (expected [x, y, z])"));
    }

    FVector StartPos((*StartPosArray)[0]->AsNumber(), (*StartPosArray)[1]->AsNumber(), (*StartPosArray)[2]->AsNumber());
    FVector StartTangent((*StartTangentArray)[0]->AsNumber(), (*StartTangentArray)[1]->AsNumber(), (*StartTangentArray)[2]->AsNumber());
    FVector EndPos((*EndPosArray)[0]->AsNumber(), (*EndPosArray)[1]->AsNumber(), (*EndPosArray)[2]->AsNumber());
    FVector EndTangent((*EndTangentArray)[0]->AsNumber(), (*EndTangentArray)[1]->AsNumber(), (*EndTangentArray)[2]->AsNumber());

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineMeshComponent
    USplineMeshComponent *SplineMeshComponent = Cast<USplineMeshComponent>(ComponentNode->ComponentTemplate);
    if (!SplineMeshComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineMeshComponent"), *ComponentName));
    }

    // Set start and end
    SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleSetSplineMeshStartScale(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    // Get scale
    const TArray<TSharedPtr<FJsonValue>> *ScaleArray;
    if (!Params->TryGetArrayField(TEXT("scale"), ScaleArray) || ScaleArray->Num() != 2)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid scale parameter (expected [x, y])"));
    }

    bool bScaleForward = true;
    Params->TryGetBoolField(TEXT("scale_forward"), bScaleForward);

    FVector2D Scale((*ScaleArray)[0]->AsNumber(), (*ScaleArray)[1]->AsNumber());

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineMeshComponent
    USplineMeshComponent *SplineMeshComponent = Cast<USplineMeshComponent>(ComponentNode->ComponentTemplate);
    if (!SplineMeshComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineMeshComponent"), *ComponentName));
    }

    // Set start scale
    SplineMeshComponent->SetStartScale(Scale, bScaleForward);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleSetSplineMeshEndScale(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    // Get scale
    const TArray<TSharedPtr<FJsonValue>> *ScaleArray;
    if (!Params->TryGetArrayField(TEXT("scale"), ScaleArray) || ScaleArray->Num() != 2)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid scale parameter (expected [x, y])"));
    }

    bool bScaleForward = true;
    Params->TryGetBoolField(TEXT("scale_forward"), bScaleForward);

    FVector2D Scale((*ScaleArray)[0]->AsNumber(), (*ScaleArray)[1]->AsNumber());

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineMeshComponent
    USplineMeshComponent *SplineMeshComponent = Cast<USplineMeshComponent>(ComponentNode->ComponentTemplate);
    if (!SplineMeshComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineMeshComponent"), *ComponentName));
    }

    // Set end scale
    SplineMeshComponent->SetEndScale(Scale, bScaleForward);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleAddSplinePoint(const TSharedPtr<FJsonObject> &Params)
{
    // Log all received parameters for debugging
    FString ParamsString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ParamsString);
    FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);
    UE_LOG(LogTemp, Warning, TEXT("HandleAddSplinePoint received params: %s"), *ParamsString);

    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    // Get position (try both "position" and "location" for compatibility)
    const TArray<TSharedPtr<FJsonValue>> *PositionArray;
    if (!Params->TryGetArrayField(TEXT("position"), PositionArray))
    {
        // Try "location" as fallback
        if (!Params->TryGetArrayField(TEXT("location"), PositionArray))
        {
            UE_LOG(LogTemp, Warning, TEXT("Failed to get position or location array field"));
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing position/location parameter"));
        }
    }

    if (PositionArray->Num() != 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("Position array has %d elements, expected 3"), PositionArray->Num());
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid position parameter (expected [x, y, z])"));
    }

    FVector Position((*PositionArray)[0]->AsNumber(), (*PositionArray)[1]->AsNumber(), (*PositionArray)[2]->AsNumber());

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineComponent
    USplineComponent *SplineComponent = Cast<USplineComponent>(ComponentNode->ComponentTemplate);
    if (!SplineComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineComponent"), *ComponentName));
    }

    // Add spline point
    SplineComponent->AddSplinePoint(Position, ESplineCoordinateSpace::Local);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    ResultObj->SetNumberField(TEXT("point_count"), SplineComponent->GetNumberOfSplinePoints());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleSetSplinePointLocation(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    int32 PointIndex;
    if (!Params->TryGetNumberField(TEXT("point_index"), PointIndex))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing point_index parameter"));
    }

    // Get position
    const TArray<TSharedPtr<FJsonValue>> *PositionArray;
    if (!Params->TryGetArrayField(TEXT("position"), PositionArray) || PositionArray->Num() != 3)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid position parameter (expected [x, y, z])"));
    }

    FVector Position((*PositionArray)[0]->AsNumber(), (*PositionArray)[1]->AsNumber(), (*PositionArray)[2]->AsNumber());

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineComponent
    USplineComponent *SplineComponent = Cast<USplineComponent>(ComponentNode->ComponentTemplate);
    if (!SplineComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineComponent"), *ComponentName));
    }

    // Set spline point location
    SplineComponent->SetLocationAtSplinePoint(PointIndex, Position, ESplineCoordinateSpace::Local);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    ResultObj->SetNumberField(TEXT("point_index"), PointIndex);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleGetLocationAtSplinePoint(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    int32 PointIndex;
    if (!Params->TryGetNumberField(TEXT("point_index"), PointIndex))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing point_index parameter"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineComponent
    USplineComponent *SplineComponent = Cast<USplineComponent>(ComponentNode->ComponentTemplate);
    if (!SplineComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineComponent"), *ComponentName));
    }

    // Get location at spline point
    FVector Location = SplineComponent->GetLocationAtSplinePoint(PointIndex, ESplineCoordinateSpace::Local);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    ResultObj->SetNumberField(TEXT("point_index"), PointIndex);

    TArray<TSharedPtr<FJsonValue>> LocationArray;
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.X));
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.Y));
    LocationArray.Add(MakeShared<FJsonValueNumber>(Location.Z));
    ResultObj->SetArrayField(TEXT("location"), LocationArray);

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleSetSplineTangentAtPoint(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing blueprint_name parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing component_name parameter"));
    }

    int32 PointIndex;
    if (!Params->TryGetNumberField(TEXT("point_index"), PointIndex))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing point_index parameter"));
    }

    // Get tangent
    const TArray<TSharedPtr<FJsonValue>> *TangentArray;
    if (!Params->TryGetArrayField(TEXT("tangent"), TangentArray) || TangentArray->Num() != 3)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or invalid tangent parameter (expected [x, y, z])"));
    }

    FVector Tangent((*TangentArray)[0]->AsNumber(), (*TangentArray)[1]->AsNumber(), (*TangentArray)[2]->AsNumber());

    bool bUpdateSpline = true;
    Params->TryGetBoolField(TEXT("update_spline"), bUpdateSpline);

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node *ComponentNode = nullptr;
    if (Blueprint->SimpleConstructionScript)
    {
        for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
        {
            if (Node && Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    // Check if it's a SplineComponent
    USplineComponent *SplineComponent = Cast<USplineComponent>(ComponentNode->ComponentTemplate);
    if (!SplineComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component %s is not a SplineComponent"), *ComponentName));
    }

    // Set tangent at spline point
    SplineComponent->SetTangentAtSplinePoint(PointIndex, Tangent, ESplineCoordinateSpace::Local, bUpdateSpline);

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
    ResultObj->SetStringField(TEXT("component_name"), ComponentName);
    ResultObj->SetNumberField(TEXT("point_index"), PointIndex);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPSplineCommands::HandleTestSplineParams(const TSharedPtr<FJsonObject> &Params)
{
    // Log all received parameters for debugging
    FString ParamsString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ParamsString);
    FJsonSerializer::Serialize(Params.ToSharedRef(), Writer);
    UE_LOG(LogTemp, Warning, TEXT("HandleTestSplineParams received params: %s"), *ParamsString);

    // Create success response with the received parameters
    TSharedPtr<FJsonObject> ResultObj = MakeShareable(new FJsonObject);
    ResultObj->SetStringField(TEXT("status"), TEXT("success"));
    ResultObj->SetStringField(TEXT("received_params"), ParamsString);

    // Check if position field exists
    const TArray<TSharedPtr<FJsonValue>> *PositionArray;
    if (Params->TryGetArrayField(TEXT("position"), PositionArray))
    {
        ResultObj->SetStringField(TEXT("position_found"), TEXT("yes"));
        ResultObj->SetNumberField(TEXT("position_length"), PositionArray->Num());
    }
    else
    {
        ResultObj->SetStringField(TEXT("position_found"), TEXT("no"));
    }

    return ResultObj;
}