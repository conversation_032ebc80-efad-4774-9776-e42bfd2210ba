LoginId:b4f508ed4bd797d4247ed9a748e6dcab
EpicAccountId:6460d6b6163c4887bdafe06ad4082897

Assertion failed: FoundPin != 0 [File:D:\build\++UE5\Sync\Engine\Source\Editor\BlueprintGraph\Classes\K2Node_AddComponent.h] [Line: 77] 



UnrealEditor_Core
UnrealEditor_BlueprintGraph
UnrealEditor_BlueprintGraph
UnrealEditor_UnrealMCP!FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintAddSplineMeshComponent() [C:\Auracron\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintNodeCommands.cpp:2323]
UnrealEditor_UnrealMCP!FUnrealMCPBlueprintNodeCommands::HandleCommand() [C:\Auracron\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintNodeCommands.cpp:137]
UnrealEditor_UnrealMCP!`UUnrealMCPBridge::ExecuteCommand'::`2'::<lambda_1>::operator()() [C:\Auracron\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp:291]
UnrealEditor_Core
UnrealEditor_Core
UnrealEditor_Core
UnrealEditor_Core
UnrealEditor_Core
UnrealEditor_Core
UnrealEditor_MassEntityEditor
UnrealEditor_UnrealEd
UnrealEditor_Engine
UnrealEditor_UnrealEd
UnrealEditor_UnrealEd
UnrealEditor
UnrealEditor
UnrealEditor
UnrealEditor
UnrealEditor
UnrealEditor
kernel32
ntdll