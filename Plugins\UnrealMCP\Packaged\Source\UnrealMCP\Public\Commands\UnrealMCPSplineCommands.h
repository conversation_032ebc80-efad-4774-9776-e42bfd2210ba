#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"

/**
 * Commands for handling Spline-specific operations in UnrealMCP
 */
class UNREALMCP_API FUnrealMCPSplineCommands
{
public:
    FUnrealMCPSplineCommands();

    /**
     * Handle spline-related commands
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString &CommandType, const TSharedPtr<FJsonObject> &Params);

private:
    /**
     * Set static mesh properties on a SplineMeshComponent
     */
    TSharedPtr<FJsonObject> HandleSetSplineMeshProperties(const TSharedPtr<FJsonObject> &Params);

    /**
     * Set start and end properties on a SplineMeshComponent
     */
    TSharedPtr<FJsonObject> HandleSetSplineMeshStartAndEnd(const TSharedPtr<FJsonObject> &Params);

    /**
     * Set start scale on a SplineMeshComponent
     */
    TSharedPtr<FJsonObject> HandleSetSplineMeshStartScale(const TSharedPtr<FJsonObject> &Params);

    /**
     * Set end scale on a SplineMeshComponent
     */
    TSharedPtr<FJsonObject> HandleSetSplineMeshEndScale(const TSharedPtr<FJsonObject> &Params);

    /**
     * Add spline point to a SplineComponent
     */
    TSharedPtr<FJsonObject> HandleAddSplinePoint(const TSharedPtr<FJsonObject> &Params);

    /**
     * Set spline point location
     */
    TSharedPtr<FJsonObject> HandleSetSplinePointLocation(const TSharedPtr<FJsonObject> &Params);

    /**
     * Get location at spline point
     */
    TSharedPtr<FJsonObject> HandleGetLocationAtSplinePoint(const TSharedPtr<FJsonObject> &Params);

    /**
     * Set spline tangent at point
     */
    TSharedPtr<FJsonObject> HandleSetSplineTangentAtPoint(const TSharedPtr<FJsonObject> &Params);

    /**
     * Test spline parameters (debug function)
     */
    TSharedPtr<FJsonObject> HandleTestSplineParams(const TSharedPtr<FJsonObject> &Params);
};