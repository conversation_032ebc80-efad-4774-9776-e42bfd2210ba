# PowerShell script to fix all remaining node_position = [0, 0] occurrences
$filePath = "c:\Auracron\Python\tools\node_tools.py"

Write-Host "🔧 Corrigindo posicionamento de nós em: $filePath" -ForegroundColor Yellow

# Read the file content
$content = Get-Content $filePath -Raw

# Count occurrences before
$beforeCount = ([regex]::Matches($content, "node_position = \[0, 0\]")).Count
Write-Host "📊 Encontradas $beforeCount ocorrências de 'node_position = [0, 0]'" -ForegroundColor Cyan

# Replace all occurrences
$newContent = $content -replace "node_position = \[0, 0\]", "node_position = get_auto_node_position()"

# Count occurrences after
$afterCount = ([regex]::Matches($newContent, "node_position = \[0, 0\]")).Count

# Write back to file
$newContent | Set-Content $filePath -NoNewline

Write-Host "✅ Substituições realizadas:" -ForegroundColor Green
Write-Host "   • Antes: $beforeCount ocorrências" -ForegroundColor White
Write-Host "   • Depois: $afterCount ocorrências" -ForegroundColor White
Write-Host "   • Corrigidas: $($beforeCount - $afterCount) ocorrências" -ForegroundColor Green

if ($afterCount -eq 0) {
    Write-Host "🎉 SUCESSO: Todas as ocorrências foram corrigidas!" -ForegroundColor Green
} else {
    Write-Host "⚠️  ATENÇÃO: Ainda restam $afterCount ocorrências" -ForegroundColor Yellow
}

Write-Host "`n🚀 Agora todos os nós serão posicionados automaticamente!" -ForegroundColor Magenta
