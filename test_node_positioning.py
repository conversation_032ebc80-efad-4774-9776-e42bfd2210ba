#!/usr/bin/env python3
"""
Teste do sistema de posicionamento automático de nós.
Este script demonstra como os nós serão posicionados automaticamente.
"""

# Simular o sistema de posicionamento
_node_position_counter = 0

def get_auto_node_position():
    """Generate automatic node positions to avoid overlap"""
    global _node_position_counter
    
    # Create a grid layout with nodes spaced 300 units apart
    # This ensures nodes don't overlap and creates a readable layout
    nodes_per_row = 4
    spacing_x = 300
    spacing_y = 200
    
    row = _node_position_counter // nodes_per_row
    col = _node_position_counter % nodes_per_row
    
    x = col * spacing_x
    y = row * spacing_y
    
    _node_position_counter += 1
    
    return [x, y]

def reset_node_position_counter():
    """Reset the node position counter for a new blueprint"""
    global _node_position_counter
    _node_position_counter = 0

def get_sequential_position(base_x=0, base_y=0, offset_x=400):
    """Get position for nodes that should be in sequence (like in a construction script)"""
    global _node_position_counter
    x = base_x + (_node_position_counter * offset_x)
    y = base_y
    _node_position_counter += 1
    return [x, y]

# Demonstração do posicionamento automático
print("=== DEMONSTRAÇÃO DO POSICIONAMENTO AUTOMÁTICO DE NÓS ===\n")

print("🔄 Resetando contador para novo Blueprint...")
reset_node_position_counter()

print("\n📍 Posições automáticas em grid (4 nós por linha):")
nodes = [
    "UserConstructionScript",
    "For Each Loop", 
    "Array Clear",
    "Array Length",
    "Branch",
    "Add SplineMeshComponent",
    "Set StartAndEnd",
    "Set Static Mesh",
    "Set Mobility",
    "Set Collision Profile"
]

for i, node_name in enumerate(nodes):
    pos = get_auto_node_position()
    print(f"  {i+1:2d}. {node_name:<25} → Posição: [{pos[0]:3d}, {pos[1]:3d}]")

print(f"\n📊 Layout resultante:")
print(f"   Linha 1: [  0,   0] [300,   0] [600,   0] [900,   0]")
print(f"   Linha 2: [  0, 200] [300, 200] [600, 200] [900, 200]")
print(f"   Linha 3: [  0, 400] [300, 400]")

print("\n🔄 Resetando para demonstrar posicionamento sequencial...")
reset_node_position_counter()

print("\n➡️  Posições sequenciais (para Construction Script):")
construction_nodes = [
    "UserConstructionScript",
    "Array Clear", 
    "For Each Loop",
    "Branch",
    "Add SplineMeshComponent"
]

for i, node_name in enumerate(construction_nodes):
    pos = get_sequential_position(base_x=100, base_y=100, offset_x=400)
    print(f"  {i+1}. {node_name:<25} → Posição: [{pos[0]:3d}, {pos[1]:3d}]")

print(f"\n📊 Layout sequencial resultante:")
print(f"   [100, 100] → [500, 100] → [900, 100] → [1300, 100] → [1700, 100]")

print("\n✅ VANTAGENS DO NOVO SISTEMA:")
print("   • ❌ ANTES: Todos os nós em (0, 0) - sobrepostos e ilegíveis")
print("   • ✅ AGORA: Nós distribuídos automaticamente - layout organizado")
print("   • 🎯 Espaçamento adequado (300x200) evita sobreposição")
print("   • 🔧 Posicionamento manual ainda funciona quando especificado")
print("   • 📐 Layout em grid ou sequencial conforme necessário")

print("\n🚀 RESULTADO: Blueprints mais legíveis e organizados!")
