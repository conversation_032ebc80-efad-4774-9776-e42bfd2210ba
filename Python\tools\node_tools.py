"""
Blueprint Node Tools for Unreal MCP.

This module provides tools for manipulating Blueprint graph nodes and connections.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

# Global counter for automatic node positioning
_node_position_counter = 0

def get_auto_node_position():
    """Generate automatic node positions to avoid overlap"""
    global _node_position_counter

    # Create a grid layout with nodes spaced 300 units apart
    # This ensures nodes don't overlap and creates a readable layout
    nodes_per_row = 4
    spacing_x = 300
    spacing_y = 200

    row = _node_position_counter // nodes_per_row
    col = _node_position_counter % nodes_per_row

    x = col * spacing_x
    y = row * spacing_y

    _node_position_counter += 1

    return [x, y]

def reset_node_position_counter():
    """Reset the node position counter for a new blueprint"""
    global _node_position_counter
    _node_position_counter = 0

def get_sequential_position(base_x=0, base_y=0, offset_x=400):
    """Get position for nodes that should be in sequence (like in a construction script)"""
    global _node_position_counter
    x = base_x + (_node_position_counter * offset_x)
    y = base_y
    _node_position_counter += 1
    return [x, y]

def register_blueprint_node_tools(mcp: FastMCP):
    """Register Blueprint node manipulation tools with the MCP server."""

    @mcp.tool()
    def add_blueprint_event_node(
        ctx: Context,
        blueprint_name: str,
        event_name: str,
        node_position: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Add an event node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            event_name: Name of the event. Use 'Receive' prefix for standard events:
                       - 'ReceiveBeginPlay' for Begin Play
                       - 'ReceiveTick' for Tick
                       - etc.
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Handle default value within the method body - use auto positioning
            if node_position is None:
                node_position = get_auto_node_position()

            params = {
                "blueprint_name": blueprint_name,
                "event_name": event_name,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding event node '{event_name}' to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_event_node", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Event node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding event node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_function_node(
        ctx: Context,
        blueprint_name: str,
        target: str,
        function_name: str,
        params: Optional[str] = None,
        node_position: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Add a function call node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            target: Target object for the function (component name or self)
            function_name: Name of the function to call
            params: Optional parameters to set on the function node
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Handle default values within the method body - use auto positioning
            if params is None:
                params = {}
            if node_position is None:
                node_position = get_auto_node_position()

            command_params = {
                "blueprint_name": blueprint_name,
                "target": target,
                "function_name": function_name,
                "params": params,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding function node '{function_name}' for target '{target}' to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_function_node", command_params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Function node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding function node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_for_loop(
        ctx: Context,
        blueprint_name: str,
        node_position: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Add a For Loop node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Use auto positioning for better layout
            if node_position is None:
                node_position = get_auto_node_position()

            params = {
                "blueprint_name": blueprint_name,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding For Loop node to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_for_loop", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"For Loop node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding For Loop node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_add_spline_mesh_component(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        node_position: list = None
    ) -> Dict[str, Any]:
        """
        Add a SplineMeshComponent to a Blueprint.

        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name for the new SplineMeshComponent
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Handle None case explicitly in the function - use auto positioning
            if node_position is None:
                node_position = get_auto_node_position()

            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding SplineMeshComponent '{component_name}' to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_add_spline_mesh_component", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"SplineMeshComponent creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding SplineMeshComponent: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_spline_mesh_set_start_and_end(
        ctx: Context,
        blueprint_name: str,
        spline_mesh_component: str,
        node_position: list = None
    ) -> Dict[str, Any]:
        """
        Add a SplineMesh Set Start and End node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            spline_mesh_component: Name of the SplineMeshComponent
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Handle default value within the method body - use auto positioning
            if node_position is None:
                node_position = get_auto_node_position()

            params = {
                "blueprint_name": blueprint_name,
                "spline_mesh_component": spline_mesh_component,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding SplineMesh Set Start and End node for '{spline_mesh_component}' to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_spline_mesh_set_start_and_end", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"SplineMesh Set Start and End node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding SplineMesh Set Start and End node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_array_clear(
        ctx: Context,
        blueprint_name: str,
        array_variable: str,
        node_position: list = None
    ) -> Dict[str, Any]:
        """
        Add an Array Clear node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            array_variable: Name of the array variable to clear
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Handle default value within the method body - use auto positioning
            if node_position is None:
                node_position = get_auto_node_position()

            params = {
                "blueprint_name": blueprint_name,
                "array_variable": array_variable,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Array Clear node for '{array_variable}' to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_array_clear", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Array Clear node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Array Clear node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_array_add(
        ctx: Context,
        blueprint_name: str,
        array_variable: str,
        node_position: list = None
    ) -> Dict[str, Any]:
        """
        Add an Array Add node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            array_variable: Name of the array variable to add to
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            # Handle default value within the method body - use auto positioning
            if node_position is None:
                node_position = get_auto_node_position()

            params = {
                "blueprint_name": blueprint_name,
                "array_variable": array_variable,
                "node_position": node_position
            }

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Array Add node for '{array_variable}' to blueprint '{blueprint_name}' at position {node_position}")
            response = unreal.send_command("add_blueprint_array_add", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Array Add node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Array Add node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}